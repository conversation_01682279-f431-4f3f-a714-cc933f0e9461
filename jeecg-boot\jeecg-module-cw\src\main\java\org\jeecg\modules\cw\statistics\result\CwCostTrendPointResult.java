package org.jeecg.modules.cw.statistics.result;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 月度成本趋势点
 */
@Data
public class CwCostTrendPointResult {
    /** yyyy-MM */
    private String period;

    /** 实际总成本（万元） */
    private BigDecimal totalCost;
    /** 实际吨矿成本 */
    private BigDecimal tonCost;
    /** 实际金属成本 */
    private BigDecimal metalCost;

    /** 计划总成本（万元） */
    private BigDecimal planTotalCost;
    /** 计划吨矿成本 */
    private BigDecimal planTonCost;
    /** 计划金属成本 */
    private BigDecimal planMetalCost;
}