<template>
  <!-- 价量分析页面 -->
  <div class="cw-jlfx-page">
    <a-card :bordered="false">
      <template #title>
        金属利润对比 (计划 vs 实际)
      </template>
      <template #extra>
        <a-switch v-model:checked="barMode" checked-children="双柱" un-checked-children="差异" />
      </template>
      <!-- 查询条件 -->
      <a-space style="margin-bottom: 12px;" align="center">
        <a-radio-group v-model:value="dimension" @change="fetchBarData">
          <a-radio-button value="month">月</a-radio-button>
          <a-radio-button value="year">年</a-radio-button>
        </a-radio-group>
        <a-date-picker
          v-model:value="barDate"
          :picker="pickerType"
          :allowClear="false"
          @change="fetchBarData"
        />
      </a-space>

      <!-- 影响概览卡片 -->
      <div class="impact-summary">
        <!-- 价格影响列表 -->
        <div class="impact-item price">
          <div class="label">价格影响 (万元)</div>
          <div class="impact-list">
            <div class="impact-row" v-for="p in priceImpactList" :key="p.metal">
              <span class="metal">{{ p.metal }}</span>
              <span :class="p.value >= 0 ? 'pos' : 'neg'">{{ formatNumber(p.value) }}</span>
            </div>
          </div>
          <a-divider style="margin:6px 0" />
          <div class="total">合计：{{ formatNumber(totalPriceImpact) }}</div>
        </div>
        <!-- 产量影响列表 -->
        <div class="impact-item volume">
          <div class="label">产量影响 (万元)</div>
          <div class="impact-list">
            <div class="impact-row" v-for="v in volumeImpactList" :key="v.metal">
              <span class="metal">{{ v.metal }}</span>
              <span :class="v.value >= 0 ? 'pos' : 'neg'">{{ formatNumber(v.value) }}</span>
            </div>
          </div>
          <a-divider style="margin:6px 0" />
          <div class="total">合计：{{ formatNumber(totalVolumeImpact) }}</div>
        </div>
      </div>

      <div ref="barRef" class="bar-chart"></div>
    </a-card>
  </div>
</template>

<script lang="ts" setup name="cw-price-volume-statistics">
  // 价量分析脚本
  import { ref, onMounted, watch, computed } from 'vue';
  import dayjs from 'dayjs';
  import { message } from 'ant-design-vue';

  // 接口
  import { metalProfitBar } from '/@/api/cw/statistics';
  // 工具
  import { useECharts } from '/@/hooks/web/useECharts';
  import { formatNumber } from '/@/utils/showUtils';

  /** 查询条件 */
  const dimension = ref<'month' | 'year'>('month');
  const barDate = ref(dayjs());
  const pickerType = computed(() => dimension.value);

  /** 图表实例 */
  const barRef = ref<HTMLDivElement | null>(null);
  // @ts-ignore
  const { setOptions: setBarOptions } = useECharts(barRef as any);

  /** 图表类型切换：false->差异模式，true->双柱模式 */
  const BAR_MODE_KEY = 'cwMetalProfitBarMode';
  const barMode = ref<boolean>(localStorage.getItem(BAR_MODE_KEY) === 'double');
  watch(barMode, (val) => {
    localStorage.setItem(BAR_MODE_KEY, val ? 'double' : 'diff');
    updateBarChart();
  });

  /** 数据源 */
  const barList = ref<any[]>([]);

  /** 影响汇总 */
  const totalPriceImpact = computed(() => {
    return barList.value.reduce((sum, item) => sum + Number(item.priceImpact ?? 0), 0);
  });
  const totalVolumeImpact = computed(() => {
    return barList.value.reduce((sum, item) => sum + Number(item.volumeImpact ?? 0), 0);
  });

  /** 分金属影响列表 */
  const priceImpactList = computed(() => {
    return barList.value.map((item: any) => ({ metal: item.metal, value: Number(item.priceImpact ?? 0) }));
  });
  const volumeImpactList = computed(() => {
    return barList.value.map((item: any) => ({ metal: item.metal, value: Number(item.volumeImpact ?? 0) }));
  });

  /** 监听变化自动刷新 */
  watch([dimension, barDate], () => fetchBarData());

  onMounted(() => {
    fetchBarData();
  });

  /** 获取柱状图数据 */
  async function fetchBarData() {
    try {
      const dateStr = barDate.value.format('YYYY-MM-DD');
      barList.value = await metalProfitBar({ date: dateStr, dimension: dimension.value });
      updateBarChart();
    } catch (e) {
      console.error(e);
      message.error('金属利润数据获取失败');
    }
  }

  /** 更新柱状图 */
  function updateBarChart() {
    if (!barList.value?.length) return;

    const metals = barList.value.map((d) => d.metal || '--');
    const planVals = barList.value.map((d) => Number(d.plan ?? 0));
    const actualVals = barList.value.map((d) => Number(d.actual ?? 0));

    if (!barMode.value) {
      // 差异模式
      const diffVals = actualVals.map((a, idx) => Number((a - planVals[idx]).toFixed(4)));
    const minDiff = Math.min(...diffVals);
    setBarOptions({
      tooltip: {
        trigger: 'axis',
        axisPointer: { type: 'shadow' },
        formatter: (params: any) => {
          const p = params as any[];
          if (!p?.length) return '';
          const plan = p.find((i) => i.seriesName === '计划');
          const diff = p.find((i) => i.seriesName === '差异');
          const actualVal = (plan?.value ?? 0) + (diff?.value ?? 0);
          return `${p[0].name}<br/>计划：${formatNumber(plan?.value)}<br/>实际：${formatNumber(actualVal)}<br/>差异：${formatNumber(diff?.value)}`;
        },
      },
      grid: { left: 80, right: 40, bottom: 60, top: 30, containLabel: true },
      legend: { data: ['计划', '差异'], top: 'bottom' },
      xAxis: { type: 'category', data: metals },
      yAxis: {
        type: 'value',
        min: minDiff < 0 ? minDiff : 0,
          axisLabel: { formatter: (v: any) => formatNumber(v) },
      },
      series: [
        {
          name: '计划',
          type: 'bar',
          stack: 'profit',
          barWidth: '40%',
          itemStyle: { color: '#91caff' },
          label: { show: true, position: 'inside', formatter: '{c}' },
          data: planVals,
        },
        {
          name: '差异',
          type: 'bar',
          stack: 'profit',
          barWidth: '40%',
          label: {
            show: true,
            position: diffVals.some((v) => v < 0) ? 'insideBottom' : 'inside',
            formatter: (v: any) => (v.value >= 0 ? '+' + v.value : v.value),
          },
          itemStyle: {
            color: (param: any) => (param.value >= 0 ? '#ff7875' : '#95de64'),
          },
          data: diffVals,
        },
      ],
      } as any);
    } else {
      // 双柱模式
      setBarOptions({
        tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' } },
        grid: { left: 80, right: 40, bottom: 60, top: 30, containLabel: true },
        legend: { data: ['计划', '实际'], top: 'bottom' },
        xAxis: { type: 'category', data: metals },
        yAxis: { type: 'value', axisLabel: { formatter: (v: any) => formatNumber(v) } },
        series: [
          { name: '计划', type: 'bar', barWidth: '40%', itemStyle: { color: '#91caff' }, data: planVals },
          { name: '实际', type: 'bar', barWidth: '40%', itemStyle: { color: '#ff7875' }, data: actualVals },
        ],
      } as any);
    }
  }
</script>

<style scoped lang="less">
.cw-jlfx-page {
  .ant-card {
    margin-bottom: 16px;
    .bar-chart {
      width: 100%;
      height: calc(100vh - 200px); // 视口自适应高度
    }

    /* ==== Impact Cards Modern Style ==== */
    .impact-summary {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
      gap: 16px;
      margin: 12px 0;
    }

    .impact-item {
      background: #ffffff;
      border-radius: 10px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      padding: 16px 20px;
      display: flex;
      flex-direction: column;
    }

    .impact-item .label {
      font-size: 16px;
      font-weight: 600;
      color: #595959;
      margin-bottom: 8px;
    }

    .impact-list {
      flex: 1;
      border-top: 1px dashed #f0f0f0;
      padding-top: 8px;
      margin-top: 8px;
    }

    .impact-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 6px 4px; /* 增加左右间隙 */
      font-size: 14px;
    }

    .impact-row:not(:last-child) {
      border-bottom: 1px dashed #f0f0f0;
    }

    .impact-row .metal {
      display: inline-flex;
      align-items: center;
      gap: 4px;
    }

    .impact-row .metal::before {
      content: "";
      width: 6px;
      height: 6px;
      border-radius: 50%;
      background-color: #1890ff;
    }

    .impact-row .pos {
      color: #f5222d;
    }
    .impact-row .neg {
      color: #52c41a;
    }

    .total {
      margin-top: 10px;
      font-weight: 600;
      text-align: right;
      font-size: 18px; /* 更突出 */
    }
  }
}
</style>
