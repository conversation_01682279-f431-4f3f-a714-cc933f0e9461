# 采矿场综合成本表计算逻辑文档

## 一、表格行结构

### 1. 处理量预测行
- **数据来源**：`CwCllycData`表中的`cllyc`字段
- **显示逻辑**：从当月第一天获取处理量预测数据

### 2. 采剥总量行
- **数据来源**：`CwDwBase`表中的`cbzl`字段
- **显示逻辑**：显示当天的采剥总量数据

### 3. 其他成本项行
- **数据来源**：`CwNameDict`表中定义的各成本项

## 二、表格列计算逻辑

### 1. 月预测列
- **数据来源**：用户填写（16 号以后可填写，下半月与月末各填写一次）
- **填写规则**：
  1. 每月 16-月末可填写一次"下半月月预测"。
  2. 月末（当月最后一天）可再次填写"月末月预测"，覆盖下半月预测。
- **显示状态**：根据日期显示"下半月"或"月末"标签

### 2. 当日数列
- **数据来源**：完全由系统计算生成，不存储在数据库中
- **存储状态**：数据库中已删除当日数字段，仅在前端展示时实时计算
- **计算公式**：
  - 当日期为 1-15 号（上半月）时：
    ```
    单位成本 = 月预算 ÷ 处理量预测
    当日数   = 单位成本 × 采剥总量
    ```
  - 当日期为 16-月末（下半月及月末）时：
    ```
    单位成本 = 月预测 ÷ 处理量预测
    当日数   = 单位成本 × 采剥总量
    ```
- **单位成本计算**：单位成本 = 月预测 ÷ 处理量预测
- **计算条件**：月预测、处理量预测、采剥总量都不为空且处理量预测大于0
- **特殊情况**：当采剥量为空时，当日数为空，但不影响历史数据的月累计计算
- **计算逻辑**：
  ```java
  // 辅助方法，集中计算当日数
  private BigDecimal calculateDrs(String name, Date recordTime, BigDecimal cllyc, BigDecimal cbzl, List<CwCkcMonth> months) {
      // 获取该记录对应的月预测
      BigDecimal yyc = null;
      for (CwCkcMonth month : months) {
          if (name.equals(month.getName())) {
              // 判断该记录是上半月还是下半月
              int recordDay = DateUtil.dayOfMonth(recordTime);
              boolean recordIsFirstHalf = recordDay <= 15;
              
              if (recordIsFirstHalf && month.getFirstHalfForecast() != null) {
                  yyc = month.getFirstHalfForecast();
              } else if (!recordIsFirstHalf && month.getSecondHalfForecast() != null) {
                  yyc = month.getSecondHalfForecast();
              }
              break;
          }
      }
      
      // 只有当月预测、处理量预测和采剥总量都有值时，才计算当日数
      if (ObjectUtil.isNotEmpty(cllyc) && ObjectUtil.isNotEmpty(yyc) && 
          cllyc.compareTo(BigDecimal.ZERO) > 0 && ObjectUtil.isNotEmpty(cbzl)) {
          // 单位成本 = 月预测 / 处理量预测
          BigDecimal dwcb = yyc.divide(cllyc, 10, BigDecimal.ROUND_HALF_UP);
          // 当日数 = 单位成本 * 采剥总量
          return dwcb.multiply(cbzl);
      }
      return null;
  }
  ```

### 3. 月累计列
- **计算公式**：月累计 = 本月内当日之前所有天的当日数之和（不包含当日）
- **数据来源**：所有当日数全部通过计算得到，不存储在数据库中
- **特殊情况**：
  - 即使当天采剥量为空导致当日数为空，月累计仍然会显示之前有效数据的累计值
  - 如果月内某一天的采剥量为空导致当日数为空，这一天会被跳过，不会影响其他天数据的累计
  - 例如：1、2、4、5号每天的当日数都为1，3号为空，则1、2、3、4、5号的月累计分别为0、1、2、2、3
- **计算逻辑**：
  ```java
  月累计 = 0
  
  // 获取本月第一天到查询日期前一天的所有日期
  List<Date> allDatesInMonth = 获取本月所有日期列表()
  
  // 为每个日期计算当日数并累计
  for (每一天的日期 : allDatesInMonth) {
    if (日期 < 当前查询日期) {
      // 获取该日期对应的处理量预测和采剥总量
      
      // 计算当日数
      计算得到的当日数 = calculateDrs(...)
      
      // 如果当日数不为空，则累计
      if (计算得到的当日数 != null) {
        月累计 += 计算得到的当日数
      }
      // 如果当日数为空，则跳过该天，不影响累计结果
    }
  }
  ```

### 4. 平均单价列
- **计算公式**：平均单价 = 月累计 ÷ 采剥总量月累计
- **计算逻辑**：
  ```java
  if (采剥总量月累计 != null && 采剥总量月累计 > 0 && 月累计 != null) {
    平均单价 = 月累计 / 采剥总量月累计
  }
  ```

### 5. 平均单耗列
- **计算公式**：平均单耗 = 月累计 ÷ 处理量月累计
- **计算逻辑**：
  ```java
  if (处理量月累计 != null && 处理量月累计 > 0 && 月累计 != null) {
    平均单耗 = 月累计 / 处理量月累计
  }
  ```

### 6. 平均总耗列
- **计算公式**：平均总耗 = 月累计 ÷ 当月天数
- **计算逻辑**：
  ```java
  if (月累计 != null && 当月天数 > 0) {
    平均总耗 = 月累计 / 当月天数
  }
  ```

### 7. 月预算列（新增计算逻辑）
- **数据来源**：用户填写（1-15 号期间填写，一月仅一次）
- **计算作用**：上半月 (1-15 号) 相关计算全部使用月预算，而非月预测
- **显示状态**：当日期在 1-15 号时可编辑月预算，其余时间只读

### 8. 节超比列
- **计算公式**：节超比 = (月预算 - 月累计) ÷ 月预算 × 100%
- **计算逻辑**：
  ```java
  if (月预算 != null && 月预算 != 0 && 月累计 != null) {
    节超比 = (月预算 - 月累计) / 月预算 * 100%
    // 正数表示节约，负数表示超支
  }
  ```

## 三、月预测填写状态管理

1. **判断逻辑**：
   ```java
   int currentDay = DateUtil.dayOfMonth(queryDate);
   boolean isFirstHalf = currentDay <= 15;
   ```

2. **显示标签**：
   - 当日期在1-15号时，显示"上半月"
   - 当日期在16-31号时，显示"下半月"

3. **填写状态记录**：
   - 使用API请求记录月预测填写状态
   - 月预算：每月仅能填写一次（1-15 号期间）。
   - 月预测：每月可填写两次（16-月末一次，月末最后一天再次填写覆盖）。 